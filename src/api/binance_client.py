"""
币安API客户端封装
提供统一的API接口，支持现货和期货交易
"""

import time
import logging
from typing import Dict, List, Optional, Any, Tuple
from decimal import Decimal
import pandas as pd
from binance.client import Client
from binance.exceptions import BinanceAPIException, BinanceOrderException
from config.config import config

logger = logging.getLogger(__name__)


class BinanceClient:
    """币安API客户端封装类"""
    
    def __init__(self):
        """初始化币安客户端"""
        self.api_key = config.BINANCE_API_KEY
        self.secret_key = config.BINANCE_SECRET_KEY
        self.testnet = config.BINANCE_TESTNET
        
        if not self.api_key or not self.secret_key:
            raise ValueError("币安API密钥未配置，请检查.env文件")
        
        # 初始化客户端
        self.client = Client(
            api_key=self.api_key,
            api_secret=self.secret_key,
            testnet=self.testnet
        )
        
        # 缓存交易对信息
        self._exchange_info = None
        self._symbol_info = {}
        
        logger.info(f"币安客户端初始化完成 (测试网: {self.testnet})")
    
    def test_connection(self) -> bool:
        """测试API连接"""
        try:
            self.client.ping()
            account_info = self.client.get_account()
            logger.info("币安API连接测试成功")
            logger.info(f"账户状态: {account_info.get('accountType', 'Unknown')}")
            return True
        except Exception as e:
            logger.error(f"币安API连接测试失败: {e}")
            return False
    
    def get_account_info(self) -> Dict[str, Any]:
        """获取账户信息"""
        try:
            account = self.client.get_account()
            balances = []
            
            for balance in account['balances']:
                free = float(balance['free'])
                locked = float(balance['locked'])
                if free > 0 or locked > 0:
                    balances.append({
                        'asset': balance['asset'],
                        'free': free,
                        'locked': locked,
                        'total': free + locked
                    })
            
            return {
                'account_type': account.get('accountType', 'SPOT'),
                'can_trade': account.get('canTrade', False),
                'can_withdraw': account.get('canWithdraw', False),
                'can_deposit': account.get('canDeposit', False),
                'balances': balances,
                'total_balance_usdt': self._calculate_total_balance_usdt(balances)
            }
        except BinanceAPIException as e:
            logger.error(f"获取账户信息失败: {e}")
            raise
    
    def get_symbol_info(self, symbol: str) -> Dict[str, Any]:
        """获取交易对信息"""
        if symbol in self._symbol_info:
            return self._symbol_info[symbol]
        
        try:
            if not self._exchange_info:
                self._exchange_info = self.client.get_exchange_info()
            
            for s in self._exchange_info['symbols']:
                if s['symbol'] == symbol:
                    info = {
                        'symbol': s['symbol'],
                        'status': s['status'],
                        'base_asset': s['baseAsset'],
                        'quote_asset': s['quoteAsset'],
                        'price_precision': s['quotePrecision'],
                        'quantity_precision': s['baseAssetPrecision'],
                        'min_qty': 0,
                        'max_qty': 0,
                        'step_size': 0,
                        'min_notional': 0
                    }
                    
                    # 解析过滤器
                    for f in s['filters']:
                        if f['filterType'] == 'LOT_SIZE':
                            info['min_qty'] = float(f['minQty'])
                            info['max_qty'] = float(f['maxQty'])
                            info['step_size'] = float(f['stepSize'])
                        elif f['filterType'] == 'MIN_NOTIONAL':
                            info['min_notional'] = float(f['minNotional'])
                    
                    self._symbol_info[symbol] = info
                    return info
            
            raise ValueError(f"交易对 {symbol} 不存在")
            
        except BinanceAPIException as e:
            logger.error(f"获取交易对信息失败: {e}")
            raise
    
    def get_ticker_price(self, symbol: str) -> float:
        """获取交易对当前价格"""
        try:
            ticker = self.client.get_symbol_ticker(symbol=symbol)
            return float(ticker['price'])
        except BinanceAPIException as e:
            logger.error(f"获取价格失败 {symbol}: {e}")
            raise
    
    def get_orderbook(self, symbol: str, limit: int = 100) -> Dict[str, Any]:
        """获取订单簿"""
        try:
            orderbook = self.client.get_order_book(symbol=symbol, limit=limit)
            return {
                'symbol': symbol,
                'bids': [[float(price), float(qty)] for price, qty in orderbook['bids']],
                'asks': [[float(price), float(qty)] for price, qty in orderbook['asks']],
                'timestamp': orderbook['lastUpdateId']
            }
        except BinanceAPIException as e:
            logger.error(f"获取订单簿失败 {symbol}: {e}")
            raise
    
    def get_klines(self, symbol: str, interval: str, limit: int = 500, 
                   start_time: Optional[int] = None, end_time: Optional[int] = None) -> pd.DataFrame:
        """获取K线数据"""
        try:
            klines = self.client.get_klines(
                symbol=symbol,
                interval=interval,
                limit=limit,
                startTime=start_time,
                endTime=end_time
            )
            
            df = pd.DataFrame(klines, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_volume', 'trades', 'taker_buy_base',
                'taker_buy_quote', 'ignore'
            ])
            
            # 转换数据类型
            numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'quote_volume']
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col])
            
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            
            return df[['open', 'high', 'low', 'close', 'volume']]
            
        except BinanceAPIException as e:
            logger.error(f"获取K线数据失败 {symbol}: {e}")
            raise

    def place_order(self, symbol: str, side: str, order_type: str,
                    quantity: float, price: Optional[float] = None,
                    time_in_force: str = 'GTC') -> Dict[str, Any]:
        """下单"""
        try:
            # 获取交易对信息进行参数验证
            symbol_info = self.get_symbol_info(symbol)

            # 调整数量精度
            quantity = self._adjust_quantity(quantity, symbol_info)

            order_params = {
                'symbol': symbol,
                'side': side,
                'type': order_type,
                'quantity': quantity,
                'timeInForce': time_in_force
            }

            if price is not None:
                # 调整价格精度
                price = self._adjust_price(price, symbol_info)
                order_params['price'] = price

            # 下单
            if order_type == 'MARKET':
                if side == 'BUY':
                    order = self.client.order_market_buy(**{k: v for k, v in order_params.items() if k != 'timeInForce'})
                else:
                    order = self.client.order_market_sell(**{k: v for k, v in order_params.items() if k != 'timeInForce'})
            else:
                order = self.client.create_order(**order_params)

            logger.info(f"订单创建成功: {symbol} {side} {quantity} @ {price}")
            return self._format_order_response(order)

        except BinanceOrderException as e:
            logger.error(f"下单失败: {e}")
            raise
        except BinanceAPIException as e:
            logger.error(f"API错误: {e}")
            raise

    def cancel_order(self, symbol: str, order_id: int) -> Dict[str, Any]:
        """取消订单"""
        try:
            result = self.client.cancel_order(symbol=symbol, orderId=order_id)
            logger.info(f"订单取消成功: {symbol} {order_id}")
            return self._format_order_response(result)
        except BinanceAPIException as e:
            logger.error(f"取消订单失败: {e}")
            raise

    def get_order_status(self, symbol: str, order_id: int) -> Dict[str, Any]:
        """查询订单状态"""
        try:
            order = self.client.get_order(symbol=symbol, orderId=order_id)
            return self._format_order_response(order)
        except BinanceAPIException as e:
            logger.error(f"查询订单状态失败: {e}")
            raise

    def get_open_orders(self, symbol: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取未完成订单"""
        try:
            orders = self.client.get_open_orders(symbol=symbol)
            return [self._format_order_response(order) for order in orders]
        except BinanceAPIException as e:
            logger.error(f"获取未完成订单失败: {e}")
            raise

    def get_order_history(self, symbol: str, limit: int = 500) -> List[Dict[str, Any]]:
        """获取历史订单"""
        try:
            orders = self.client.get_all_orders(symbol=symbol, limit=limit)
            return [self._format_order_response(order) for order in orders]
        except BinanceAPIException as e:
            logger.error(f"获取历史订单失败: {e}")
            raise

    def _calculate_total_balance_usdt(self, balances: List[Dict]) -> float:
        """计算总余额(USDT)"""
        total = 0.0

        # 主要的稳定币，直接按1:1计算
        stable_coins = ['USDT', 'USDC', 'BUSD', 'TUSD', 'FDUSD', 'USDP']

        # 法币资产，需要特殊处理或跳过
        fiat_currencies = [
            'TRY', 'ZAR', 'UAH', 'BRL', 'EUR', 'PLN', 'RON', 'ARS',
            'JPY', 'MXN', 'COP', 'CZK', 'AEUR', 'EURI'
        ]

        # 已知无效的交易对（没有对USDT的交易对）
        invalid_symbols = set(fiat_currencies + [
            'DAI',  # DAI没有DAIUSDT交易对，但可以按1:1计算
        ])

        for balance in balances:
            asset = balance['asset']
            amount = balance['total']

            if amount <= 0:
                continue

            if asset in stable_coins or asset == 'DAI':
                # 稳定币按1:1计算
                total += amount
                logger.debug(f"稳定币 {asset}: {amount:.2f} (按1:1计算)")
            elif asset in fiat_currencies:
                # 法币资产跳过，因为没有对应的USDT交易对
                logger.debug(f"跳过法币资产 {asset}: {amount:.2f}")
                continue
            else:
                # 其他资产，尝试获取价格
                try:
                    symbol = f"{asset}USDT"
                    price = self.get_ticker_price(symbol)
                    asset_value = amount * price
                    total += asset_value
                    logger.debug(f"{asset}: {amount:.6f} * {price:.6f} = {asset_value:.2f} USDT")
                except Exception as e:
                    # 跳过无法定价的资产
                    logger.debug(f"无法获取 {asset} 价格，跳过: {e}")
                    continue

        return total

    def _adjust_quantity(self, quantity: float, symbol_info: Dict) -> float:
        """调整数量精度"""
        step_size = symbol_info['step_size']
        precision = len(str(step_size).split('.')[-1]) if '.' in str(step_size) else 0
        return round(quantity, precision)

    def _adjust_price(self, price: float, symbol_info: Dict) -> float:
        """调整价格精度"""
        precision = symbol_info['price_precision']
        return round(price, precision)

    def _format_order_response(self, order: Dict) -> Dict[str, Any]:
        """格式化订单响应"""
        return {
            'order_id': int(order['orderId']),
            'symbol': order['symbol'],
            'side': order['side'],
            'type': order['type'],
            'quantity': float(order['origQty']),
            'price': float(order['price']) if order['price'] != '0.00000000' else None,
            'executed_qty': float(order['executedQty']),
            'status': order['status'],
            'time_in_force': order['timeInForce'],
            'timestamp': int(order['time']),
            'update_time': int(order['updateTime'])
        }
